import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../constants/app_theme.dart';
import '../../models/trip_model.dart';
import '../../models/user_model.dart';
import '../../providers/trip_provider.dart';
import '../../services/storage_service.dart';
import '../../services/supabase_service.dart';
import '../../widgets/verification_badge.dart';
import '../../widgets/rating_stars.dart';

class TripDetailsPage extends StatefulWidget {
  final String tripId;

  const TripDetailsPage({
    super.key,
    required this.tripId,
  });

  @override
  State<TripDetailsPage> createState() => _TripDetailsPageState();
}

class _TripDetailsPageState extends State<TripDetailsPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<TripProvider>(context, listen: false)
          .loadTripById(widget.tripId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<TripProvider>(
        builder: (context, tripProvider, child) {
          if (tripProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (tripProvider.selectedTrip == null) {
            return const Center(
              child: Text('لم يتم العثور على الرحلة'),
            );
          }

          final trip = tripProvider.selectedTrip!;
          return _buildTripDetails(trip);
        },
      ),
    );
  }

  Widget _buildTripDetails(TripModel trip) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(trip),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildRouteSection(trip),
                const SizedBox(height: 24),
                _buildTripInfoSection(trip),
                const SizedBox(height: 24),
                _buildDriverSection(trip),
                const SizedBox(height: 24),
                _buildCarSection(trip),
                const SizedBox(height: 24),
                _buildPricingSection(trip),
                const SizedBox(height: 24),
                if (trip.amenities.isNotEmpty) ...[
                  _buildAmenitiesSection(trip),
                  const SizedBox(height: 24),
                ],
                if (trip.rules.isNotEmpty) ...[
                  _buildRulesSection(trip),
                  const SizedBox(height: 24),
                ],
                if (trip.notes != null && trip.notes!.isNotEmpty) ...[
                  _buildNotesSection(trip),
                  const SizedBox(height: 24),
                ],
                _buildActionButtons(trip),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar(TripModel trip) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          '${trip.fromCity} ← ${trip.toCity}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: trip.imageUrls.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: trip.imageUrls.first,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.primary,
                  child: const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primary, AppColors.primaryLight],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.directions_car,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
            : Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.primaryLight],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.directions_car,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildRouteSection(TripModel trip) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color:
                        _getTripTypeColor(trip.tripType).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getTripTypeColor(trip.tripType),
                      width: 1.5,
                    ),
                  ),
                  child: Text(
                    _getTripTypeText(trip.tripType),
                    style: TextStyle(
                      color: _getTripTypeColor(trip.tripType),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.secondary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${trip.price.toInt()} درهم',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              trip.fromCity,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        margin: const EdgeInsets.only(right: 6),
                        width: 2,
                        height: 30,
                        color: AppColors.textTertiary,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: AppColors.secondary,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              trip.toCity,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.access_time,
                        color: AppColors.primary,
                        size: 24,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _calculateDuration(trip),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      const Text(
                        'مدة الرحلة',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripInfoSection(TripModel trip) {
    final dateFormat = DateFormat('EEEE، dd MMMM yyyy', 'ar');
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الرحلة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _InfoCard(
                    icon: Icons.calendar_today,
                    title: 'تاريخ المغادرة',
                    value: dateFormat.format(trip.departureDate),
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _InfoCard(
                    icon: Icons.access_time,
                    title: 'وقت المغادرة',
                    value: trip.departureTime,
                    color: AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _InfoCard(
                    icon: Icons.airline_seat_recline_normal,
                    title: 'المقاعد المتاحة',
                    value: '${trip.availableSeats} من ${trip.totalSeats}',
                    color: AppColors.accent,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _InfoCard(
                    icon: Icons.schedule,
                    title: 'نوع الحجز',
                    value: trip.allowInstantBooking ? 'فوري' : 'بموافقة',
                    color: trip.allowInstantBooking
                        ? AppColors.success
                        : AppColors.warning,
                  ),
                ),
              ],
            ),
            // Add duration info if available
            if (trip.formattedDuration.isNotEmpty) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _InfoCard(
                      icon: Icons.timer,
                      title: 'مدة الرحلة المتوقعة',
                      value: trip.formattedDuration,
                      color: AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                      child: SizedBox()), // Empty space for alignment
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDriverSection(TripModel trip) {
    // Get driver information - use leader data or fallback to leaderId
    final leaderName = trip.leader?.fullName.trim();
    final driverName = (leaderName != null && leaderName.isNotEmpty)
        ? leaderName
        : 'سائق غير معروف';
    final driverId = trip.leader?.id ?? trip.leaderId;
    final isVerified = trip.leader?.isVerified ?? false;
    final driverRating = trip.leader?.rating ?? 0.0;
    final totalTrips = trip.leader?.totalTrips ?? 0;

    // Debug information
    if (kDebugMode) {
      print('Driver Section Debug:');
      print('  Trip ID: ${trip.id}');
      print('  Leader ID: ${trip.leaderId}');
      print('  Leader Object: ${trip.leader != null ? 'Present' : 'Null'}');
      if (trip.leader != null) {
        print('  Leader Full Name: "${trip.leader!.fullName}"');
        print('  Leader Profile URL: "${trip.leader!.profileImageUrl}"');
      }
      print('  Final Driver Name: "$driverName"');
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات السائق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildDriverAvatar(
                    driverId, driverName, trip.leader?.profileImageUrl),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      VerifiedUserName(
                        name: driverName,
                        isVerified: isVerified,
                        textStyle: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        badgeSize: 18.0,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          RatingStars(
                            rating: driverRating,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            driverRating > 0
                                ? driverRating.toStringAsFixed(1)
                                : 'جديد',
                            style: const TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            '$totalTrips رحلة',
                            style: const TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverAvatar(
      String driverId, String driverName, String? storedProfileUrl) {
    // Generate profile image URL using the correct format
    final profileImageUrl = StorageService.getProfileImageUrl(
      driverId,
      storedUrl: storedProfileUrl,
    );

    // Get first letter of name for fallback
    final firstLetter =
        driverName.isNotEmpty ? driverName.substring(0, 1).toUpperCase() : 'ق';

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipOval(
        child: profileImageUrl != null && profileImageUrl.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: profileImageUrl,
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primary, AppColors.secondary],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) {
                  debugPrint(
                      'Profile image loading error for $driverId: $error');
                  return Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.primary, AppColors.secondary],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        firstLetter,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  );
                },
              )
            : Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.secondary],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Center(
                  child: Text(
                    firstLetter,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildCarSection(TripModel trip) {
    if (trip.carModel == null &&
        trip.carColor == null &&
        trip.carPlateNumber == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات السيارة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.directions_car,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (trip.carModel != null)
                          Text(
                            trip.carModel!,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            if (trip.carColor != null) ...[
                              Text(
                                'اللون: ${trip.carColor}',
                                style: const TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 14,
                                ),
                              ),
                              if (trip.carPlateNumber != null)
                                const Text(' • '),
                            ],
                            if (trip.carPlateNumber != null)
                              Text(
                                'اللوحة: ${trip.carPlateNumber}',
                                style: const TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 14,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection(TripModel trip) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل السعر',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.secondary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'سعر المقعد الواحد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${trip.price.toInt()} درهم',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.secondary,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'السعر شامل جميع الرسوم',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmenitiesSection(TripModel trip) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المرافق المتاحة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: trip.amenities
                  .map((amenity) => _AmenityChip(amenity: amenity))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRulesSection(TripModel trip) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قوانين الرحلة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...trip.rules.map((rule) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: AppColors.success,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          rule,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection(TripModel trip) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات إضافية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: AppColors.info,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      trip.notes!,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(TripModel trip) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: trip.availableSeats > 0 ? () => _bookTrip(trip) : null,
            icon: const Icon(Icons.book_online, size: 20),
            label: Text(
              trip.availableSeats > 0 ? 'احجز الآن' : 'لا توجد مقاعد متاحة',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: trip.availableSeats > 0
                  ? AppColors.secondary
                  : AppColors.textTertiary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _sendMessage(trip),
            icon: const Icon(Icons.message, size: 20),
            label: const Text('إرسال رسالة للسائق'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary, width: 1.5),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper methods
  String _calculateDuration(TripModel trip) {
    // Mock calculation - in real app, this would use route calculation
    final distance = _estimateDistance(trip.fromCity, trip.toCity);
    final hours = (distance / 80).round(); // Assuming 80 km/h average speed
    return '${hours}س تقريباً';
  }

  double _estimateDistance(String fromCity, String toCity) {
    // Mock distance calculation - in real app, use Google Maps API
    final distances = {
      'الرباط-الدار البيضاء': 90.0,
      'الدار البيضاء-مراكش': 240.0,
      'الرباط-فاس': 200.0,
      'الدار البيضاء-أكادير': 250.0,
      'فاس-مكناس': 60.0,
    };

    final key1 = '$fromCity-$toCity';
    final key2 = '$toCity-$fromCity';

    return distances[key1] ?? distances[key2] ?? 150.0;
  }

  Color _getTripTypeColor(String tripType) {
    switch (tripType) {
      case 'women_only':
        return Colors.pink;
      case 'family_only':
        return Colors.orange;
      case 'men_only':
        return Colors.blue;
      default:
        return AppColors.primary;
    }
  }

  String _getTripTypeText(String tripType) {
    switch (tripType) {
      case 'women_only':
        return 'نسائية';
      case 'family_only':
        return 'عائلية';
      case 'men_only':
        return 'ذكورية';
      default:
        return 'مختلطة';
    }
  }

  void _bookTrip(TripModel trip) {
    // TODO: Implement booking logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ عملية الحجز قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _sendMessage(TripModel trip) {
    // TODO: Implement messaging logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ نظام الرسائل قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}

// Helper Widgets
class _InfoCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final Color color;

  const _InfoCard({
    required this.icon,
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

class _AmenityChip extends StatelessWidget {
  final String amenity;

  const _AmenityChip({required this.amenity});

  @override
  Widget build(BuildContext context) {
    final amenityInfo = _getAmenityInfo(amenity);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            amenityInfo['icon'],
            size: 16,
            color: AppColors.info,
          ),
          const SizedBox(width: 6),
          Text(
            amenityInfo['text'],
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.info,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getAmenityInfo(String amenity) {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return {'icon': Icons.wifi, 'text': 'واي فاي'};
      case 'ac':
      case 'air_conditioning':
        return {'icon': Icons.ac_unit, 'text': 'تكييف'};
      case 'music':
        return {'icon': Icons.music_note, 'text': 'موسيقى'};
      case 'charging':
        return {'icon': Icons.battery_charging_full, 'text': 'شحن'};
      case 'water':
        return {'icon': Icons.local_drink, 'text': 'ماء'};
      case 'snacks':
        return {'icon': Icons.fastfood, 'text': 'وجبات خفيفة'};
      default:
        return {'icon': Icons.check_circle, 'text': amenity};
    }
  }
}
