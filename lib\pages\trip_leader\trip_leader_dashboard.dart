import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/trip_provider.dart';
import '../../models/trip_model.dart';
import '../../models/booking_model.dart';
import '../../services/storage_service.dart';
import '../../widgets/trip_card.dart';
import '../../widgets/trip_leader_cards.dart';
import '../../utils/navigation_utils.dart';
import 'create_trip_page.dart';
import 'manage_trip_page.dart';
import 'trip_leader_profile_page.dart';

class TripLeaderDashboard extends StatefulWidget {
  const TripLeaderDashboard({super.key});

  @override
  State<TripLeaderDashboard> createState() => _TripLeaderDashboardState();
}

class _TripLeaderDashboardState extends State<TripLeaderDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _pendingRequestsCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadDashboardData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadDashboardData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final tripProvider = Provider.of<TripProvider>(context, listen: false);

    if (authProvider.currentUser != null) {
      tripProvider.loadUserTrips(authProvider.currentUser!.id);
      _updatePendingRequestsCount();
    }
  }

  void _updatePendingRequestsCount() {
    // TODO: Load pending requests count from Supabase
    setState(() {
      _pendingRequestsCount = 3; // Mock data
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              backgroundColor: AppColors.primary,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppColors.primaryGradient,
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // Profile Picture
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: ClipOval(
                              child: authProvider
                                          .currentUser?.profileImageUrl !=
                                      null
                                  ? CachedNetworkImage(
                                      imageUrl:
                                          StorageService.getProfileImageUrl(
                                                authProvider.currentUser!.id,
                                                storedUrl: authProvider
                                                    .currentUser!
                                                    .profileImageUrl,
                                              ) ??
                                              '',
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) => Container(
                                        width: 60,
                                        height: 60,
                                        color: AppColors.primary,
                                        child: const Center(
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        ),
                                      ),
                                      errorWidget: (context, url, error) {
                                        debugPrint(
                                            'Profile image loading error: $error');
                                        return Container(
                                          width: 60,
                                          height: 60,
                                          color: AppColors.primary,
                                          child: const Icon(
                                            Icons.person,
                                            size: 30,
                                            color: Colors.white,
                                          ),
                                        );
                                      },
                                    )
                                  : Container(
                                      width: 60,
                                      height: 60,
                                      color: AppColors.primary,
                                      child: const Icon(
                                        Icons.person,
                                        size: 30,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          ),
                          const SizedBox(width: 16),

                          // User Info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  authProvider.currentUser?.fullName ??
                                      'قائد الرحلة',
                                  style: theme.textTheme.titleLarge?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.verified,
                                      size: 16,
                                      color:
                                          Colors.white.withValues(alpha: 0.9),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'قائد رحلات معتمد',
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        color:
                                            Colors.white.withValues(alpha: 0.9),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // Notifications
                          Stack(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.notifications,
                                    color: Colors.white),
                                onPressed: () {
                                  // TODO: Navigate to notifications
                                },
                              ),
                              if (_pendingRequestsCount > 0)
                                Positioned(
                                  right: 8,
                                  top: 8,
                                  child: Container(
                                    padding: const EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    constraints: const BoxConstraints(
                                      minWidth: 16,
                                      minHeight: 16,
                                    ),
                                    child: Text(
                                      '$_pendingRequestsCount',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.person, color: Colors.white),
                  onPressed: () {
                    NavigationUtils.pushWithTransition(
                      context,
                      const TripLeaderProfilePage(),
                      type: TransitionType.slide,
                    );
                  },
                ),
              ],
            ),
          ];
        },
        body: Column(
          children: [
            // Create Trip Button
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final user = authProvider.currentUser;
                  final canCreateTrips = user?.canCreateTrips ?? false;

                  return Column(
                    children: [
                      ElevatedButton.icon(
                        onPressed: canCreateTrips
                            ? () {
                                NavigationUtils.pushWithTransition(
                                  context,
                                  const CreateTripPage(),
                                  type: TransitionType.slide,
                                );
                              }
                            : null,
                        icon: const Icon(Icons.add),
                        label: const Text('إنشاء رحلة جديدة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: canCreateTrips
                              ? AppColors.secondary
                              : AppColors.textSecondary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),

                      // Balance warning if insufficient
                      if (!canCreateTrips && user != null) ...[
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.orange.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning,
                                  color: Colors.orange, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'تحتاج إلى رصيد 5 درهم على الأقل لإنشاء رحلات جديدة. رصيدك الحالي: ${user.displayBalance}',
                                  style: TextStyle(
                                    color: Colors.orange.shade700,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),

            // Balance and Stats Section
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final user = authProvider.currentUser;
                  if (user == null) return const SizedBox.shrink();

                  return Row(
                    children: [
                      // Balance
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.account_balance_wallet,
                                    color: AppColors.secondary, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'الرصيد',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: AppColors.textSecondary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user.displayBalance,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    color: AppColors.secondary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                      ),

                      // Total Trips
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.directions_car,
                                    color: AppColors.primary, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'إجمالي الرحلات',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: AppColors.textSecondary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${user.totalTrips}',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                      ),

                      // Rating
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.star,
                                    color: Colors.amber, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'التقييم',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: AppColors.textSecondary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user.displayRating,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    color: Colors.amber.shade700,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Tab Bar
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.directions_car),
                        const SizedBox(width: 8),
                        const Text('الرحلات النشطة'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.check_circle),
                        const SizedBox(width: 8),
                        const Text('المكتملة'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.pending),
                        const SizedBox(width: 8),
                        const Text('الطلبات'),
                        if (_pendingRequestsCount > 0) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              '$_pendingRequestsCount',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  _ActiveTripsTab(),
                  _CompletedTripsTab(),
                  _PendingRequestsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Active Trips Tab
class _ActiveTripsTab extends StatelessWidget {
  const _ActiveTripsTab();

  @override
  Widget build(BuildContext context) {
    return Consumer<TripProvider>(
      builder: (context, tripProvider, child) {
        // Show published trips (active trips for passengers)
        final activeTrips = tripProvider.userTrips
            .where((trip) =>
                trip.status == AppConstants.tripStatusPublished ||
                trip.status == AppConstants.tripStatusActive)
            .toList();

        if (tripProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (activeTrips.isEmpty) {
          return _buildEmptyState(
            context,
            icon: Icons.directions_car_outlined,
            title: 'لا توجد رحلات نشطة',
            subtitle: 'ابدأ بإنشاء رحلتك الأولى',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: activeTrips.length,
          itemBuilder: (context, index) {
            return ActiveTripCard(trip: activeTrips[index]);
          },
        );
      },
    );
  }
}

// Completed Trips Tab
class _CompletedTripsTab extends StatelessWidget {
  const _CompletedTripsTab();

  @override
  Widget build(BuildContext context) {
    return Consumer<TripProvider>(
      builder: (context, tripProvider, child) {
        final completedTrips = tripProvider.userTrips
            .where((trip) => trip.status == AppConstants.tripStatusCompleted)
            .toList();

        if (tripProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (completedTrips.isEmpty) {
          return _buildEmptyState(
            context,
            icon: Icons.check_circle_outline,
            title: 'لا توجد رحلات مكتملة',
            subtitle: 'ستظهر رحلاتك المكتملة هنا',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: completedTrips.length,
          itemBuilder: (context, index) {
            return CompletedTripCard(trip: completedTrips[index]);
          },
        );
      },
    );
  }
}

// Pending Requests Tab
class _PendingRequestsTab extends StatelessWidget {
  const _PendingRequestsTab();

  @override
  Widget build(BuildContext context) {
    // TODO: Load pending booking requests from Supabase
    final pendingRequests = <BookingModel>[]; // Mock empty list

    if (pendingRequests.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.pending_outlined,
        title: 'لا توجد طلبات معلقة',
        subtitle: 'ستظهر طلبات الحجز هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: pendingRequests.length,
      itemBuilder: (context, index) {
        return PendingRequestCard(booking: pendingRequests[index]);
      },
    );
  }
}

Widget _buildEmptyState(
  BuildContext context, {
  required IconData icon,
  required String title,
  required String subtitle,
}) {
  final theme = Theme.of(context);

  return Center(
    child: Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}
